services:
  fastapi-app:
    build: .
    ports:
      - "8002:8000"
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - DATABASE_NAME=${DATABASE_NAME}
      - COLLECTION_NAME=${COLLECTION_NAME}
    depends_on:
      - mongodb
    volumes:
      - .:/app
    networks:
      - app-network

  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network

volumes:
  mongodb_data:

networks:
  app-network:
    driver: bridge
