# FastAPI MongoDB Project

Dự án FastAPI để đọc dữ liệu từ MongoDB với Docker support.

## 🚀 Tính năng

- ✅ FastAPI với async/await
- ✅ MongoDB integration với Motor
- ✅ Docker và Docker Compose
- ✅ Environment configuration
- ✅ API documentation tự động (Swagger/OpenAPI)
- ✅ 3 filter chính: merchant_name, created between, transaction_ids in
- ✅ CORS support
- ✅ Logging
- ✅ Error handling

## 📋 Yêu cầu

- Python 3.11+
- Docker và Docker Compose
- MongoDB (nếu chạy local)

## ⚙️ Cài đặt

### 1. Clone và setup

```bash
git clone <repository-url>
cd log_request_a12
```

### 2. Cấu hình environment

Sao chép file `.env.example` thành `.env` và cập nhật thông tin:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```env
# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=your_database_name
COLLECTION_NAME=logs_request_api

# FastAPI Configuration
APP_NAME=FastAPI MongoDB App
DEBUG=True
HOST=0.0.0.0
PORT=8002
```

## 🐳 Chạy với Docker

### Chạy toàn bộ stack (FastAPI + MongoDB)

```bash
docker-compose up -d
```

### Chỉ chạy FastAPI (nếu đã có MongoDB)

```bash
docker build -t fastapi-app .
docker run -p 8002:8002 --env-file .env fastapi-app
```

## 🐍 Chạy local (không Docker)

### 1. Tạo virtual environment

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows
```

### 2. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 3. Chạy ứng dụng

```bash
python main.py
# hoặc
uvicorn main:app --reload --host 0.0.0.0 --port 8002
```

## 📚 API Endpoints

Sau khi chạy ứng dụng, truy cập:

- **API Documentation**: http://localhost:8002/docs
- **ReDoc**: http://localhost:8002/redoc
- **Health Check**: http://localhost:8002/health

### Các endpoint chính:

#### 📊 API Logs Endpoints

##### 1. General Logs API (3 filters):
```
GET /api/v1/logs/
```

**Parameters:**
- `limit` (int): Số lượng logs (1-1000, default: 20)
- `skip` (int): Số logs bỏ qua (default: 0)
- `merchant_name` (string): Tên merchant (exact match)
- `start_date` (string): Ngày bắt đầu (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)
- `end_date` (string): Ngày kết thúc (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)
- `transaction_ids` (string): Danh sách transaction IDs, phân cách bằng dấu phẩy

##### 2. Grab API (merchant_name = "GrabApi"):
```
GET /api/v1/grab/logs/
GET /api/v1/grab/stats/
```

**Parameters cho /grab/logs/:**
- `limit` (int): Số lượng logs (1-1000, default: 20)
- `skip` (int): Số logs bỏ qua (default: 0)
- `start_date` (string): Ngày bắt đầu (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)
- `end_date` (string): Ngày kết thúc (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)
- `transaction_ids` (string): Danh sách transaction IDs, phân cách bằng dấu phẩy

**Ví dụ sử dụng:**

```bash
# General API - Lấy logs của merchant cụ thể
GET /api/v1/logs/?merchant_name=VmgApi&limit=50

# General API - Kết hợp tất cả filters
GET /api/v1/logs/?merchant_name=VmgApi&start_date=2024-01-01&end_date=2024-01-31&transaction_ids=tx001,tx002

# Grab API - Lấy logs GrabApi
GET /api/v1/grab/logs/?limit=50

# Grab API - Filter theo thời gian
GET /api/v1/grab/logs/?start_date=2024-01-01 10:00:00&end_date=2024-01-01 18:00:00

# Grab API - Filter theo transaction IDs
GET /api/v1/grab/logs/?transaction_ids=tx001,tx002,tx003

# Grab API - Thống kê
GET /api/v1/grab/stats/
```

## 🔧 Cấu trúc dự án

```
.
├── app/
│   ├── __init__.py
│   ├── database.py      # MongoDB connection
│   ├── models.py        # Pydantic models chung
│   ├── log_models.py    # Models cho logs
│   ├── routes.py        # API routes chung
│   └── logs_routes.py   # API routes cho logs
├── .env                 # Environment variables
├── .env.example         # Environment template
├── .gitignore
├── docker-compose.yml
├── Dockerfile
├── main.py             # FastAPI app
├── requirements.txt
└── README.md
```

## 🛠️ Development

### Thêm endpoint mới

1. Thêm model vào `app/models.py`
2. Thêm route vào `app/routes.py`
3. Import route trong `main.py`

### Logging

Logs được cấu hình trong `main.py`. Để xem logs:

```bash
# Docker
docker-compose logs -f fastapi-app

# Local
# Logs sẽ hiển thị trong terminal
```

## 🐛 Troubleshooting

### Lỗi kết nối MongoDB

1. Kiểm tra `MONGODB_URL` trong `.env`
2. Đảm bảo MongoDB đang chạy
3. Kiểm tra network connectivity

### Lỗi collection không tồn tại

1. Kiểm tra `DATABASE_NAME` và `COLLECTION_NAME` trong `.env`
2. Đảm bảo collection có dữ liệu

### Port đã được sử dụng

```bash
# Tìm process đang sử dụng port 8002
lsof -i :8002

# Kill process
kill -9 <PID>
```

## 🧪 Testing

Để test các API endpoints:

```bash
# Cài đặt requests nếu chưa có
pip install requests

# Test General API
python test_api.py

# Test Grab API riêng
python test_grab_api.py

# Test response format (3 trường)
python test_response_format.py

# Test Grab API flattened format
python test_grab_flattened.py
```

## 📊 Response Format

### General API (`/api/v1/logs/`)
Trả về 3 trường gốc:

```json
{
  "success": true,
  "message": "Lấy thành công X logs",
  "data": [
    {
      "response_data": {...},
      "request_data": {...},
      "created": "2025-06-25T08:12:39.992000"
    }
  ],
  "total": 1521,
  "count": 1
}
```

### Grab API (`/api/v1/grab/logs/`)
Trả về dữ liệu với mỗi gift thành một record riêng:

```json
{
  "success": true,
  "message": "Lấy thành công 51 Grab API logs",
  "data": [
    {
      "uuid": "11b693fb1eb74348bc79e84a028fe2ea",
      "url": "https://api.grab.com/gifts/v2/go?id=...",
      "start_date": "2025-06-25T08:12:40Z",
      "end_date": "2025-12-22T08:12:40Z",
      "currency": "VND",
      "value": 10000,
      "short_code": "TESTJ7C9OS9S",
      "status": "ISSUED",
      "created": "2025-06-25T08:12:39.992000",
      "request_value": 10000,
      "request_quantity": 51,
      "request_countryCode": "VN",
      "request_inventories": ["grab_express"],
      "request_designKey": "thank_you",
      "request_messageTitle": "Congratulations on your gift!",
      "request_messageBody": "Hope you enjoy your GrabGift to spend on Grab services",
      "request_enableShortCode": true,
      "response_buy_orderID": "e1d791d9b42d46419b901a656f765f78",
      "response_buy_reference": "UB20250625178994426942335"
    },
    {
      "uuid": "3a0aad04f6994b2d92f359df175b05f8",
      "url": "https://api.grab.com/gifts/v2/go?id=...",
      "start_date": "2025-06-25T08:12:40Z",
      "end_date": "2025-12-22T08:12:40Z",
      "currency": "VND",
      "value": 10000,
      "short_code": "TESTTN9K83FU",
      "status": "ISSUED",
      "created": "2025-06-25T08:12:39.992000",
      "request_value": 10000,
      "request_quantity": 51,
      "request_countryCode": "VN",
      "request_inventories": ["grab_express"],
      "request_designKey": "thank_you",
      "request_messageTitle": "Congratulations on your gift!",
      "request_messageBody": "Hope you enjoy your GrabGift to spend on Grab services",
      "request_enableShortCode": true,
      "response_buy_orderID": "e1d791d9b42d46419b901a656f765f78",
      "response_buy_reference": "UB20250625178994426942335"
    }
  ],
  "total": 12855,
  "count": 51
}
```

**Lưu ý:**
- General API: Giữ nguyên cấu trúc gốc (3 trường)
- Grab API: Mỗi gift trở thành một record riêng với tất cả context data
- Grab API: 1 log gốc với 51 gifts → 51 records trong response
- Không bao gồm `_id`, `transaction_id` để tối ưu hóa

## 📝 Notes

- Dự án này chỉ hỗ trợ đọc dữ liệu (READ operations)
- Tất cả ObjectId được tự động convert thành string trong response
- CORS được enable cho tất cả origins (cần cấu hình lại cho production)
- Pagination mặc định: limit=20, skip=0
- Sắp xếp mặc định theo `created` (mới nhất trước)
- Filter hỗ trợ regex cho `merchant_name` và `request_url`
