# FastAPI MongoDB Project

Dự án FastAPI để đọc dữ liệu từ MongoDB với Docker support.

## 🚀 Tính năng

- ✅ FastAPI với async/await
- ✅ MongoDB integration với Motor
- ✅ Docker và Docker Compose
- ✅ Environment configuration
- ✅ API documentation tự động (Swagger/OpenAPI)
- ✅ CORS support
- ✅ Logging
- ✅ Error handling

## 📋 Yêu cầu

- Python 3.11+
- Docker và Docker Compose
- MongoDB (nếu chạy local)

## ⚙️ Cài đặt

### 1. Clone và setup

```bash
git clone <repository-url>
cd log_request_a12
```

### 2. Cấu hình environment

Sao chép file `.env.example` thành `.env` và cập nhật thông tin:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```env
# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=your_database_name
COLLECTION_NAME=logs_request_api

# FastAPI Configuration
APP_NAME=FastAPI MongoDB App
DEBUG=True
HOST=0.0.0.0
PORT=8000
```

## 🐳 Chạy với Docker

### Chạy toàn bộ stack (FastAPI + MongoDB)

```bash
docker-compose up -d
```

### Chỉ chạy FastAPI (nếu đã có MongoDB)

```bash
docker build -t fastapi-app .
docker run -p 8000:8000 --env-file .env fastapi-app
```

## 🐍 Chạy local (không Docker)

### 1. Tạo virtual environment

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows
```

### 2. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 3. Chạy ứng dụng

```bash
python main.py
# hoặc
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 📚 API Endpoints

Sau khi chạy ứng dụng, truy cập:

- **API Documentation**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

### Các endpoint chính:

#### 📊 API Logs Endpoints (Collection: logs_request_api)

##### 1. Lấy danh sách API logs
```
GET /api/v1/logs/?limit=20&skip=0&method=GET&status_code=200&endpoint=/api&start_date=2024-01-01&end_date=2024-01-31
```

##### 2. Lấy chi tiết một log
```
GET /api/v1/logs/{log_id}
```

##### 3. Thống kê tổng quan
```
GET /api/v1/logs/stats/summary
```

##### 4. Lấy error logs
```
GET /api/v1/logs/stats/errors?limit=50&hours=24
```

#### 📄 Generic Documents Endpoints

##### 5. Lấy tất cả documents
```
GET /api/v1/documents?limit=10&skip=0&sort_field=_id&sort_order=-1
```

##### 6. Lấy document theo ID
```
GET /api/v1/documents/{document_id}
```

##### 7. Tìm kiếm documents
```
POST /api/v1/documents/search
Content-Type: application/json

{
  "filters": {
    "field_name": "value",
    "another_field": {"$regex": "pattern"}
  }
}
```

##### 8. Lấy danh sách collections
```
GET /api/v1/collections
```

##### 9. Thống kê collection
```
GET /api/v1/stats
```

## 🔧 Cấu trúc dự án

```
.
├── app/
│   ├── __init__.py
│   ├── database.py      # MongoDB connection
│   ├── models.py        # Pydantic models chung
│   ├── log_models.py    # Models cho logs
│   ├── routes.py        # API routes chung
│   └── logs_routes.py   # API routes cho logs
├── .env                 # Environment variables
├── .env.example         # Environment template
├── .gitignore
├── docker-compose.yml
├── Dockerfile
├── main.py             # FastAPI app
├── requirements.txt
└── README.md
```

## 🛠️ Development

### Thêm endpoint mới

1. Thêm model vào `app/models.py`
2. Thêm route vào `app/routes.py`
3. Import route trong `main.py`

### Logging

Logs được cấu hình trong `main.py`. Để xem logs:

```bash
# Docker
docker-compose logs -f fastapi-app

# Local
# Logs sẽ hiển thị trong terminal
```

## 🐛 Troubleshooting

### Lỗi kết nối MongoDB

1. Kiểm tra `MONGODB_URL` trong `.env`
2. Đảm bảo MongoDB đang chạy
3. Kiểm tra network connectivity

### Lỗi collection không tồn tại

1. Kiểm tra `DATABASE_NAME` và `COLLECTION_NAME` trong `.env`
2. Đảm bảo collection có dữ liệu

### Port đã được sử dụng

```bash
# Tìm process đang sử dụng port 8000
lsof -i :8000

# Kill process
kill -9 <PID>
```

## 📝 Notes

- Dự án này chỉ hỗ trợ đọc dữ liệu (READ operations)
- Tất cả ObjectId được tự động convert thành string trong response
- CORS được enable cho tất cả origins (cần cấu hình lại cho production)
- Pagination mặc định: limit=10, skip=0
