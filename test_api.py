#!/usr/bin/env python3
"""
Script để test các API endpoints của logs_request_api
Chạy: python test_api.py
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000/api/v1"

def test_api_endpoint(endpoint, method="GET", data=None, params=None):
    """Test một API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url, params=params)
        elif method == "POST":
            response = requests.post(url, json=data, params=params)
        
        print(f"\n{'='*60}")
        print(f"Testing: {method} {endpoint}")
        print(f"URL: {response.url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'N/A')}")
            
            if 'data' in result and result['data']:
                if isinstance(result['data'], list):
                    print(f"Records returned: {len(result['data'])}")
                    if result['data']:
                        print("Sample record keys:", list(result['data'][0].keys()))
                elif isinstance(result['data'], dict):
                    print("Data keys:", list(result['data'].keys()))
            
            if 'total' in result:
                print(f"Total records: {result['total']}")
                
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Không thể kết nối tới {url}")
        print("Hãy đảm bảo server đang chạy trên port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing FastAPI MongoDB Logs API")
    print("Đảm bảo server đang chạy: uvicorn main:app --reload")
    
    # Test 1: Health check
    test_api_endpoint("/../", method="GET")
    
    # Test 2: Lấy danh sách logs
    test_api_endpoint("/logs/", params={"limit": 5})
    
    # Test 3: Lấy logs với filter
    test_api_endpoint("/logs/", params={
        "limit": 3,
        "response_code": 200
    })
    
    # Test 4: Thống kê tổng quan
    test_api_endpoint("/logs/stats/summary")
    
    # Test 5: Error logs
    test_api_endpoint("/logs/stats/errors", params={"limit": 5, "hours": 24})
    
    # Test 6: Recent logs
    test_api_endpoint("/logs/recent", params={"minutes": 60, "limit": 5})
    
    # Test 7: Lấy danh sách collections
    test_api_endpoint("/collections")
    
    # Test 8: Stats collection
    test_api_endpoint("/stats")
    
    print(f"\n{'='*60}")
    print("✅ Testing completed!")
    print("\n📚 Để xem full documentation:")
    print("   👉 http://localhost:8000/docs")
    print("   👉 http://localhost:8000/redoc")

if __name__ == "__main__":
    main()
