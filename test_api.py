#!/usr/bin/env python3
"""
Script để test API logs đơn giản
Chạy: python test_api.py
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000/api/v1"

def test_api_endpoint(endpoint, method="GET", params=None):
    """Test một API endpoint"""
    url = f"{BASE_URL}{endpoint}"

    try:
        response = requests.get(url, params=params)

        print(f"\n{'='*60}")
        print(f"Testing: {method} {endpoint}")
        print(f"URL: {response.url}")
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'N/A')}")

            if 'data' in result and result['data']:
                if isinstance(result['data'], list):
                    print(f"Records returned: {len(result['data'])}")
                    if result['data']:
                        print("Sample record keys:", list(result['data'][0].keys()))

            if 'total' in result:
                print(f"Total records: {result['total']}")

        else:
            print(f"Error: {response.text}")

    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Không thể kết nối tới {url}")
        print("Hãy đảm bảo server đang chạy trên port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing FastAPI MongoDB Logs API - Simplified Version")
    print("Đảm bảo server đang chạy: uvicorn main:app --reload")

    # Test 1: Health check
    test_api_endpoint("/../", method="GET")

    # Test 2: Lấy tất cả logs (limit 5)
    test_api_endpoint("/logs/", params={"limit": 5})

    # Test 3: Filter theo merchant_name
    test_api_endpoint("/logs/", params={
        "limit": 3,
        "merchant_name": "example_merchant"
    })

    # Test 4: Filter theo khoảng thời gian
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    today = datetime.now().strftime("%Y-%m-%d")
    test_api_endpoint("/logs/", params={
        "limit": 3,
        "start_date": yesterday,
        "end_date": today
    })

    # Test 5: Filter theo transaction_ids
    test_api_endpoint("/logs/", params={
        "limit": 3,
        "transaction_ids": "tx1,tx2,tx3"
    })

    # Test 6: Kết hợp tất cả filters
    test_api_endpoint("/logs/", params={
        "limit": 2,
        "merchant_name": "example_merchant",
        "start_date": yesterday,
        "end_date": today,
        "transaction_ids": "tx1,tx2"
    })

    print(f"\n{'='*60}")
    print("✅ Testing completed!")
    print("\n📚 Để xem API documentation:")
    print("   👉 http://localhost:8000/docs")
    print("\n🔍 Các filter hỗ trợ:")
    print("   • merchant_name: exact match")
    print("   • start_date, end_date: YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS")
    print("   • transaction_ids: danh sách phân cách bằng dấu phẩy")

if __name__ == "__main__":
    main()
