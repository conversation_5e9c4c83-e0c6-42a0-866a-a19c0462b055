#!/usr/bin/env python3
"""
Script để test Grab API với dữ liệu đã được làm phẳng
Chạy: python test_grab_flattened.py
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8002/api/v1"

def test_grab_flattened():
    """Test Grab API với dữ liệu làm phẳng"""
    url = f"{BASE_URL}/grab/logs/"
    
    try:
        response = requests.get(url, params={"limit": 1})
        
        print("🧪 Testing Grab API - Flattened Data Format")
        print(f"URL: {response.url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'N/A')}")
            print(f"Total records: {result.get('total', 0)}")
            
            if 'data' in result and result['data'] and len(result['data']) > 0:
                record = result['data'][0]
                print(f"\n📋 Flattened Record Structure:")
                print(f"Fields count: {len(record)}")
                
                # Hiển thị các trường theo nhóm
                print(f"\n🕒 Timestamp:")
                print(f"  • created: {record.get('created', 'N/A')}")
                
                print(f"\n📤 Request Data (from gift):")
                request_fields = [k for k in record.keys() if k.startswith('request_')]
                for field in sorted(request_fields):
                    print(f"  • {field}: {record[field]}")
                
                print(f"\n📥 Response Buy Data:")
                response_buy_fields = [k for k in record.keys() if k.startswith('response_buy_')]
                for field in sorted(response_buy_fields):
                    print(f"  • {field}: {record[field]}")
                
                print(f"\n🎁 Gifts Data:")
                if 'gifts' in record:
                    gifts = record['gifts']
                    print(f"  • gifts count: {len(gifts) if isinstance(gifts, list) else 'N/A'}")
                    if isinstance(gifts, list) and len(gifts) > 0:
                        first_gift = gifts[0]
                        print(f"  • first gift keys: {list(first_gift.keys())}")
                        print(f"  • first gift uuid: {first_gift.get('uuid', 'N/A')}")
                        print(f"  • first gift value: {first_gift.get('value', 'N/A')}")
                        print(f"  • first gift short_code: {first_gift.get('short_code', 'N/A')}")
                
                print(f"\n📊 Data Summary:")
                print(f"  • Request fields: {len(request_fields)}")
                print(f"  • Response buy fields: {len(response_buy_fields)}")
                print(f"  • Gifts count: {len(record.get('gifts', []))}")
                print(f"  • Total fields: {len(record)}")
                
        else:
            print(f"❌ Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Không thể kết nối tới {url}")
        print("Hãy đảm bảo server đang chạy trên port 8002")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing Grab API - Flattened Data Format")
    print("="*60)
    
    test_grab_flattened()
    
    print(f"\n{'='*60}")
    print("✅ Flattened data testing completed!")
    print("\n📋 New flattened structure:")
    print("   • created: timestamp")
    print("   • request_*: fields from gift object")
    print("   • response_buy_*: fields from response_buy")
    print("   • gifts: array of gift objects")
    print("\n🎯 Benefits:")
    print("   • Easier to access request data")
    print("   • Flattened response_buy fields")
    print("   • Direct access to gifts array")
    print("   • No nested object navigation needed")

if __name__ == "__main__":
    main()
