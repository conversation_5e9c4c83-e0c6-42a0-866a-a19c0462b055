#!/usr/bin/env python3
"""
Script để test Grab API với dữ liệu đã được làm phẳng
Chạy: python test_grab_flattened.py
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8002/api/v1"

def test_grab_flattened():
    """Test Grab API với dữ liệu được map vào từng gift"""
    url = f"{BASE_URL}/grab/logs/"

    try:
        response = requests.get(url, params={"limit": 1})

        print("🧪 Testing Grab API - Data Mapped to Each Gift")
        print(f"URL: {response.url}")
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'N/A')}")
            print(f"Total records: {result.get('total', 0)}")
            print(f"Records returned: {result.get('count', 0)}")

            if 'data' in result and result['data'] and len(result['data']) > 0:
                record = result['data'][0]
                print(f"\n📋 Each Gift Record Structure:")
                print(f"Fields count: {len(record)}")

                # Hiển thị các trường theo nhóm
                print(f"\n🎁 Gift Fields (original):")
                gift_fields = [k for k in record.keys() if not k.startswith('request_') and not k.startswith('response_buy_') and k != 'created']
                for field in sorted(gift_fields):
                    print(f"  • {field}: {record[field]}")

                print(f"\n🕒 Timestamp:")
                print(f"  • created: {record.get('created', 'N/A')}")

                print(f"\n📤 Request Data (mapped from gift):")
                request_fields = [k for k in record.keys() if k.startswith('request_')]
                for field in sorted(request_fields):
                    print(f"  • {field}: {record[field]}")

                print(f"\n📥 Response Buy Data (mapped):")
                response_buy_fields = [k for k in record.keys() if k.startswith('response_buy_')]
                for field in sorted(response_buy_fields):
                    print(f"  • {field}: {record[field]}")

                print(f"\n📊 Data Summary:")
                print(f"  • Gift fields: {len(gift_fields)}")
                print(f"  • Request fields: {len(request_fields)}")
                print(f"  • Response buy fields: {len(response_buy_fields)}")
                print(f"  • Total fields: {len(record)}")

                print(f"\n🔍 Sample Data:")
                print(f"  • Gift UUID: {record.get('uuid', 'N/A')}")
                print(f"  • Gift Value: {record.get('value', 'N/A')}")
                print(f"  • Short Code: {record.get('short_code', 'N/A')}")
                print(f"  • Request Quantity: {record.get('request_quantity', 'N/A')}")
                print(f"  • Order ID: {record.get('response_buy_orderID', 'N/A')}")

        else:
            print(f"❌ Error: {response.text}")

    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Không thể kết nối tới {url}")
        print("Hãy đảm bảo server đang chạy trên port 8002")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing Grab API - Data Mapped to Each Gift")
    print("="*60)

    test_grab_flattened()

    print(f"\n{'='*60}")
    print("✅ Gift mapping testing completed!")
    print("\n📋 New structure - Each gift becomes a record:")
    print("   • Original gift fields: uuid, url, start_date, end_date, etc.")
    print("   • created: timestamp mapped to each gift")
    print("   • request_*: fields from gift object mapped to each gift")
    print("   • response_buy_*: fields from response_buy mapped to each gift")
    print("\n🎯 Benefits:")
    print("   • Each gift is a standalone record")
    print("   • All context data available per gift")
    print("   • Easy to process individual gifts")
    print("   • No need to correlate gifts with parent data")
    print("   • Perfect for gift-centric operations")

if __name__ == "__main__":
    main()
