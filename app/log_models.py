from pydantic import BaseModel, Field
from typing import Optional, Any, Dict, List
from datetime import datetime

class LogRequestModel(BaseModel):
    """Model cho API request log"""
    id: Optional[str] = Field(None, alias="_id", description="Log ID")
    timestamp: Optional[datetime] = Field(None, description="Thời gian request")
    method: Optional[str] = Field(None, description="HTTP method")
    endpoint: Optional[str] = Field(None, description="API endpoint")
    status_code: Optional[int] = Field(None, description="HTTP status code")
    response_time: Optional[float] = Field(None, description="Response time (ms)")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    request_body: Optional[Dict[str, Any]] = Field(None, description="Request body")
    response_body: Optional[Dict[str, Any]] = Field(None, description="Response body")
    headers: Optional[Dict[str, str]] = Field(None, description="Request headers")
    query_params: Optional[Dict[str, str]] = Field(None, description="Query parameters")
    error_message: Optional[str] = Field(None, description="Error message nếu có")

    class Config:
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class LogsFilterModel(BaseModel):
    """Model cho filter logs"""
    method: Optional[str] = Field(None, description="HTTP method filter")
    status_code: Optional[int] = Field(None, description="Status code filter")
    endpoint: Optional[str] = Field(None, description="Endpoint filter (regex)")
    start_date: Optional[str] = Field(None, description="Start date (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date (YYYY-MM-DD)")
    ip_address: Optional[str] = Field(None, description="IP address filter")
    min_response_time: Optional[float] = Field(None, description="Minimum response time")
    max_response_time: Optional[float] = Field(None, description="Maximum response time")

class LogsStatsModel(BaseModel):
    """Model cho logs statistics"""
    total_requests: int
    status_code_distribution: List[Dict[str, Any]]
    method_distribution: List[Dict[str, Any]]
    top_endpoints: List[Dict[str, Any]]
    daily_requests_last_7_days: List[Dict[str, Any]]
    average_response_time: Optional[float] = None
    error_rate: Optional[float] = None
