from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure
import os
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class MongoDB:
    client: Optional[AsyncIOMotorClient] = None
    database = None

mongodb = MongoDB()

async def connect_to_mongo():
    """Tạo kết nối database"""
    try:
        mongodb.client = AsyncIOMotorClient(
            os.getenv("MONGODB_URL", "mongodb://localhost:27017")
        )
        mongodb.database = mongodb.client[os.getenv("DATABASE_NAME", "mydb")]
        
        # Test connection
        await mongodb.client.admin.command('ping')
        logger.info("Kết nối MongoDB thành công!")
        
    except ConnectionFailure as e:
        logger.error(f"Lỗi kết nối MongoDB: {e}")
        raise e

async def close_mongo_connection():
    """Đ<PERSON>g kết nối database"""
    if mongodb.client:
        mongodb.client.close()
        logger.info("Đã đóng kết nối MongoDB")

def get_database():
    """Lấy database instance"""
    return mongodb.database
