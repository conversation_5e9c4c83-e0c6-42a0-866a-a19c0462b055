from pydantic import BaseModel, Field
from typing import Optional, Any, Dict, List
from datetime import datetime

class QueryParams(BaseModel):
    """Model cho query parameters"""
    limit: Optional[int] = Field(default=10, ge=1, le=1000, description="Số lượng bản ghi tối đa")
    skip: Optional[int] = Field(default=0, ge=0, description="Số bản ghi bỏ qua")
    sort_field: Optional[str] = Field(default=None, description="Trường để sắp xếp")
    sort_order: Optional[int] = Field(default=-1, description="Thứ tự sắp xếp (1: tăng dần, -1: gi<PERSON><PERSON> dần)")

class FilterParams(BaseModel):
    """Model cho filter parameters"""
    filters: Optional[Dict[str, Any]] = Field(default={}, description="Điều kiện lọc")

class ResponseModel(BaseModel):
    """Model cho response"""
    success: bool
    message: str
    data: Optional[Any] = None
    total: Optional[int] = None
    count: Optional[int] = None

class DocumentResponse(BaseModel):
    """Model cho single document response"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class ListResponse(BaseModel):
    """Model cho list response"""
    success: bool
    message: str
    data: Optional[List[Dict[str, Any]]] = None
    total: Optional[int] = None
    count: Optional[int] = None
    pagination: Optional[Dict[str, Any]] = None
