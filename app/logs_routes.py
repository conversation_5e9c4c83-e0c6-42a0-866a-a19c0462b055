from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import os
from bson import ObjectId
from pymongo.errors import PyMongoError
import logging

from .database import get_database
from .models import ResponseModel, DocumentResponse, ListResponse

logger = logging.getLogger(__name__)
logs_router = APIRouter(prefix="/logs", tags=["Logs Request API"])

def serialize_document(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Chuyển đổi ObjectId thành string"""
    if doc is None:
        return None
    
    if "_id" in doc and isinstance(doc["_id"], ObjectId):
        doc["_id"] = str(doc["_id"])
    
    # Xử lý nested objects
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            doc[key] = str(value)
        elif isinstance(value, dict):
            doc[key] = serialize_document(value)
        elif isinstance(value, list):
            doc[key] = [serialize_document(item) if isinstance(item, dict) else 
                       str(item) if isinstance(item, ObjectId) else item for item in value]
    
    return doc

@logs_router.get("/", response_model=ListResponse)
async def get_api_logs(
    limit: int = Query(20, ge=1, le=1000, description="Số lượng logs"),
    skip: int = Query(0, ge=0, description="Số logs bỏ qua"),
    method: Optional[str] = Query(None, description="HTTP method (GET, POST, etc.)"),
    status_code: Optional[int] = Query(None, description="HTTP status code"),
    endpoint: Optional[str] = Query(None, description="API endpoint"),
    start_date: Optional[str] = Query(None, description="Ngày bắt đầu (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="Ngày kết thúc (YYYY-MM-DD)")
):
    """Lấy danh sách API request logs với filter"""
    try:
        db = get_database()
        collection = db["logs_request_api"]
        
        # Tạo filter query
        query = {}
        
        if method:
            query["method"] = method.upper()
        
        if status_code:
            query["status_code"] = status_code
            
        if endpoint:
            query["endpoint"] = {"$regex": endpoint, "$options": "i"}
        
        # Filter theo ngày
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    date_filter["$gte"] = start_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="start_date phải có format YYYY-MM-DD")
            
            if end_date:
                try:
                    end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                    date_filter["$lt"] = end_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="end_date phải có format YYYY-MM-DD")
            
            if date_filter:
                query["timestamp"] = date_filter
        
        # Đếm tổng số logs
        total = await collection.count_documents(query)
        
        # Lấy logs với pagination, sắp xếp theo timestamp mới nhất
        cursor = collection.find(query).sort("timestamp", -1).skip(skip).limit(limit)
        logs = await cursor.to_list(length=limit)
        
        # Serialize logs
        serialized_logs = [serialize_document(log) for log in logs]
        
        return ListResponse(
            success=True,
            message=f"Lấy thành công {len(serialized_logs)} API logs",
            data=serialized_logs,
            total=total,
            count=len(serialized_logs),
            pagination={
                "skip": skip,
                "limit": limit,
                "has_more": skip + len(serialized_logs) < total
            }
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@logs_router.get("/{log_id}", response_model=DocumentResponse)
async def get_log_by_id(log_id: str):
    """Lấy chi tiết một API log theo ID"""
    try:
        db = get_database()
        collection = db["logs_request_api"]
        
        # Validate ObjectId
        try:
            obj_id = ObjectId(log_id)
        except Exception:
            raise HTTPException(status_code=400, detail="Log ID không hợp lệ")
        
        # Tìm log
        log = await collection.find_one({"_id": obj_id})
        
        if not log:
            raise HTTPException(status_code=404, detail="Không tìm thấy log")
        
        # Serialize log
        serialized_log = serialize_document(log)
        
        return DocumentResponse(
            success=True,
            message="Lấy log thành công",
            data=serialized_log
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@logs_router.get("/stats/summary", response_model=ResponseModel)
async def get_logs_summary():
    """Lấy thống kê tổng quan về API logs"""
    try:
        db = get_database()
        collection = db["logs_request_api"]
        
        # Thống kê cơ bản
        total_requests = await collection.count_documents({})
        
        # Thống kê theo status code
        status_pipeline = [
            {"$group": {"_id": "$status_code", "count": {"$sum": 1}}},
            {"$sort": {"_id": 1}}
        ]
        status_stats = await collection.aggregate(status_pipeline).to_list(length=None)
        
        # Thống kê theo method
        method_pipeline = [
            {"$group": {"_id": "$method", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        method_stats = await collection.aggregate(method_pipeline).to_list(length=None)
        
        # Thống kê theo endpoint (top 10)
        endpoint_pipeline = [
            {"$group": {"_id": "$endpoint", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}},
            {"$limit": 10}
        ]
        endpoint_stats = await collection.aggregate(endpoint_pipeline).to_list(length=None)
        
        # Thống kê theo ngày (7 ngày gần nhất)
        seven_days_ago = datetime.now() - timedelta(days=7)
        daily_pipeline = [
            {"$match": {"timestamp": {"$gte": seven_days_ago}}},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$timestamp"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]
        daily_stats = await collection.aggregate(daily_pipeline).to_list(length=None)
        
        summary = {
            "total_requests": total_requests,
            "status_code_distribution": status_stats,
            "method_distribution": method_stats,
            "top_endpoints": endpoint_stats,
            "daily_requests_last_7_days": daily_stats
        }
        
        return ResponseModel(
            success=True,
            message="Lấy thống kê thành công",
            data=summary
        )
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@logs_router.get("/stats/errors", response_model=ResponseModel)
async def get_error_logs(
    limit: int = Query(50, ge=1, le=500, description="Số lượng error logs"),
    hours: int = Query(24, ge=1, le=168, description="Số giờ gần nhất để lấy errors")
):
    """Lấy danh sách error logs (status code >= 400)"""
    try:
        db = get_database()
        collection = db["logs_request_api"]
        
        # Filter errors trong khoảng thời gian
        time_filter = datetime.now() - timedelta(hours=hours)
        query = {
            "status_code": {"$gte": 400},
            "timestamp": {"$gte": time_filter}
        }
        
        # Lấy error logs
        cursor = collection.find(query).sort("timestamp", -1).limit(limit)
        error_logs = await cursor.to_list(length=limit)
        
        # Serialize logs
        serialized_logs = [serialize_document(log) for log in error_logs]
        
        # Thống kê error types
        error_stats_pipeline = [
            {"$match": query},
            {"$group": {"_id": "$status_code", "count": {"$sum": 1}}},
            {"$sort": {"count": -1}}
        ]
        error_stats = await collection.aggregate(error_stats_pipeline).to_list(length=None)
        
        result = {
            "error_logs": serialized_logs,
            "error_statistics": error_stats,
            "total_errors": len(serialized_logs),
            "time_range_hours": hours
        }
        
        return ResponseModel(
            success=True,
            message=f"Lấy thành công {len(serialized_logs)} error logs",
            data=result
        )
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")
