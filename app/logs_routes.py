from fastapi import APIRouter, HTTPException, Query
from typing import Optional, List, Dict, Any
from datetime import datetime
import os
from bson import ObjectId
from pymongo.errors import PyMongoError
import logging

from .database import get_database
from .models import ListResponse

logger = logging.getLogger(__name__)
logs_router = APIRouter(prefix="/logs", tags=["Logs Request API"])

def serialize_document(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Chuyển đổi ObjectId thành string"""
    if doc is None:
        return None
    
    if "_id" in doc and isinstance(doc["_id"], ObjectId):
        doc["_id"] = str(doc["_id"])
    
    # Xử lý nested objects
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            doc[key] = str(value)
        elif isinstance(value, dict):
            doc[key] = serialize_document(value)
        elif isinstance(value, list):
            doc[key] = [serialize_document(item) if isinstance(item, dict) else 
                       str(item) if isinstance(item, ObjectId) else item for item in value]
    
    return doc

@logs_router.get("/", response_model=ListResponse)
async def get_api_logs(
    limit: int = Query(20, ge=1, le=1000, description="Số lượng logs"),
    skip: int = Query(0, ge=0, description="Số logs bỏ qua"),
    merchant_name: Optional[str] = Query(None, description="Tên merchant (exact match)"),
    start_date: Optional[str] = Query(None, description="Ngày bắt đầu (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)"),
    end_date: Optional[str] = Query(None, description="Ngày kết thúc (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)"),
    transaction_ids: Optional[str] = Query(None, description="Danh sách transaction IDs, phân cách bằng dấu phẩy")
):
    """Lấy danh sách API request logs với filter đơn giản"""
    try:
        db = get_database()
        collection = db["logs_request_api"]

        # Tạo filter query
        query = {}

        # Filter merchant_name (exact match)
        if merchant_name:
            query["merchant_name"] = merchant_name

        # Filter transaction_ids (in list)
        if transaction_ids:
            try:
                tx_list = [tx.strip() for tx in transaction_ids.split(",") if tx.strip()]
                if tx_list:
                    query["transaction_id"] = {"$in": tx_list}
            except Exception:
                raise HTTPException(status_code=400, detail="transaction_ids format không hợp lệ")

        # Filter theo ngày (between)
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    # Hỗ trợ cả YYYY-MM-DD và YYYY-MM-DD HH:MM:SS
                    if len(start_date) == 10:  # YYYY-MM-DD
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    else:  # YYYY-MM-DD HH:MM:SS
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                    date_filter["$gte"] = start_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="start_date phải có format YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS")

            if end_date:
                try:
                    if len(end_date) == 10:  # YYYY-MM-DD
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                        # Thêm 1 ngày để include cả ngày end_date
                        from datetime import timedelta
                        end_dt = end_dt + timedelta(days=1)
                    else:  # YYYY-MM-DD HH:MM:SS
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                    date_filter["$lt"] = end_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="end_date phải có format YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS")

            if date_filter:
                query["created"] = date_filter
        
        # Đếm tổng số logs
        total = await collection.count_documents(query)
        
        # Lấy logs với pagination, sắp xếp theo created mới nhất
        # Chỉ select 3 trường cần thiết: response_data, request_data, created
        projection = {
            "response_data": 1,
            "request_data": 1,
            "created": 1,
            "_id": 0  # Loại bỏ _id
        }
        cursor = collection.find(query, projection).sort("created", -1).skip(skip).limit(limit)
        logs = await cursor.to_list(length=limit)
        
        # Không cần serialize vì đã loại bỏ ObjectId
        serialized_logs = logs
        
        return ListResponse(
            success=True,
            message=f"Lấy thành công {len(serialized_logs)} API logs",
            data=serialized_logs,
            total=total,
            count=len(serialized_logs),
            pagination={
                "skip": skip,
                "limit": limit,
                "has_more": skip + len(serialized_logs) < total
            }
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")
