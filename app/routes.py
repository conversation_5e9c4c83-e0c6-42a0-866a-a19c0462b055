from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Optional, Dict, Any, List
import os
from bson import ObjectId
from pymongo.errors import PyMongoError
import logging

from .database import get_database
from .models import QueryParams, FilterParams, ResponseModel, DocumentResponse, ListResponse

logger = logging.getLogger(__name__)
router = APIRouter()

def serialize_document(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Chuyển đổi ObjectId thành string"""
    if doc is None:
        return None
    
    if "_id" in doc and isinstance(doc["_id"], ObjectId):
        doc["_id"] = str(doc["_id"])
    
    # Xử lý nested objects
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            doc[key] = str(value)
        elif isinstance(value, dict):
            doc[key] = serialize_document(value)
        elif isinstance(value, list):
            doc[key] = [serialize_document(item) if isinstance(item, dict) else 
                       str(item) if isinstance(item, ObjectId) else item for item in value]
    
    return doc

@router.get("/documents", response_model=ListResponse)
async def get_all_documents(
    limit: int = Query(10, ge=1, le=1000, description="Số lượng bản ghi"),
    skip: int = Query(0, ge=0, description="Số bản ghi bỏ qua"),
    sort_field: Optional[str] = Query(None, description="Trường sắp xếp"),
    sort_order: int = Query(-1, description="Thứ tự sắp xếp (1: tăng, -1: giảm)")
):
    """Lấy tất cả documents từ collection"""
    try:
        db = get_database()
        collection_name = os.getenv("COLLECTION_NAME")
        
        if not collection_name:
            raise HTTPException(status_code=500, detail="COLLECTION_NAME chưa được cấu hình")
        
        collection = db[collection_name]
        
        # Tạo sort criteria
        sort_criteria = []
        if sort_field:
            sort_criteria.append((sort_field, sort_order))
        
        # Đếm tổng số documents
        total = await collection.count_documents({})
        
        # Lấy documents với pagination
        cursor = collection.find({}).skip(skip).limit(limit)
        if sort_criteria:
            cursor = cursor.sort(sort_criteria)
        
        documents = await cursor.to_list(length=limit)
        
        # Serialize documents
        serialized_docs = [serialize_document(doc) for doc in documents]
        
        return ListResponse(
            success=True,
            message=f"Lấy thành công {len(serialized_docs)} documents",
            data=serialized_docs,
            total=total,
            count=len(serialized_docs),
            pagination={
                "skip": skip,
                "limit": limit,
                "has_more": skip + len(serialized_docs) < total
            }
        )
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document_by_id(document_id: str):
    """Lấy document theo ID"""
    try:
        db = get_database()
        collection_name = os.getenv("COLLECTION_NAME")
        
        if not collection_name:
            raise HTTPException(status_code=500, detail="COLLECTION_NAME chưa được cấu hình")
        
        collection = db[collection_name]
        
        # Validate ObjectId
        try:
            obj_id = ObjectId(document_id)
        except Exception:
            raise HTTPException(status_code=400, detail="ID không hợp lệ")
        
        # Tìm document
        document = await collection.find_one({"_id": obj_id})
        
        if not document:
            raise HTTPException(status_code=404, detail="Không tìm thấy document")
        
        # Serialize document
        serialized_doc = serialize_document(document)
        
        return DocumentResponse(
            success=True,
            message="Lấy document thành công",
            data=serialized_doc
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@router.post("/documents/search", response_model=ListResponse)
async def search_documents(
    filter_params: FilterParams,
    limit: int = Query(10, ge=1, le=1000),
    skip: int = Query(0, ge=0),
    sort_field: Optional[str] = Query(None),
    sort_order: int = Query(-1)
):
    """Tìm kiếm documents với filter"""
    try:
        db = get_database()
        collection_name = os.getenv("COLLECTION_NAME")
        
        if not collection_name:
            raise HTTPException(status_code=500, detail="COLLECTION_NAME chưa được cấu hình")
        
        collection = db[collection_name]
        
        # Tạo filter query
        query = filter_params.filters or {}
        
        # Tạo sort criteria
        sort_criteria = []
        if sort_field:
            sort_criteria.append((sort_field, sort_order))
        
        # Đếm tổng số documents matching filter
        total = await collection.count_documents(query)
        
        # Lấy documents với filter và pagination
        cursor = collection.find(query).skip(skip).limit(limit)
        if sort_criteria:
            cursor = cursor.sort(sort_criteria)
        
        documents = await cursor.to_list(length=limit)
        
        # Serialize documents
        serialized_docs = [serialize_document(doc) for doc in documents]
        
        return ListResponse(
            success=True,
            message=f"Tìm thấy {len(serialized_docs)} documents",
            data=serialized_docs,
            total=total,
            count=len(serialized_docs),
            pagination={
                "skip": skip,
                "limit": limit,
                "has_more": skip + len(serialized_docs) < total
            }
        )
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@router.get("/collections", response_model=ResponseModel)
async def get_collections():
    """Lấy danh sách tất cả collections"""
    try:
        db = get_database()
        collections = await db.list_collection_names()
        
        return ResponseModel(
            success=True,
            message="Lấy danh sách collections thành công",
            data=collections,
            count=len(collections)
        )
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@router.get("/stats", response_model=ResponseModel)
async def get_collection_stats():
    """Lấy thống kê collection"""
    try:
        db = get_database()
        collection_name = os.getenv("COLLECTION_NAME")
        
        if not collection_name:
            raise HTTPException(status_code=500, detail="COLLECTION_NAME chưa được cấu hình")
        
        collection = db[collection_name]
        
        # Đếm tổng số documents
        total_docs = await collection.count_documents({})
        
        # Lấy một vài sample documents để hiển thị structure
        sample_docs = await collection.find({}).limit(3).to_list(length=3)
        serialized_samples = [serialize_document(doc) for doc in sample_docs]
        
        stats = {
            "collection_name": collection_name,
            "total_documents": total_docs,
            "sample_documents": serialized_samples
        }
        
        return ResponseModel(
            success=True,
            message="Lấy thống kê thành công",
            data=stats
        )
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")
