from fastapi import APIRouter, HTTPException, Query
from typing import Optional, List, Dict, Any
from datetime import datetime
import os
from bson import ObjectId
from pymongo.errors import PyMongoError
import logging

from .database import get_database
from .models import ListResponse

logger = logging.getLogger(__name__)
grab_router = APIRouter(prefix="/grab", tags=["Grab API Logs"])

def serialize_document(doc: Dict[str, Any]) -> Dict[str, Any]:
    """Chuyển đổi ObjectId thành string"""
    if doc is None:
        return None
    
    if "_id" in doc and isinstance(doc["_id"], ObjectId):
        doc["_id"] = str(doc["_id"])
    
    # Xử lý nested objects
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            doc[key] = str(value)
        elif isinstance(value, dict):
            doc[key] = serialize_document(value)
        elif isinstance(value, list):
            doc[key] = [serialize_document(item) if isinstance(item, dict) else 
                       str(item) if isinstance(item, ObjectId) else item for item in value]
    
    return doc

@grab_router.get("/logs/", response_model=ListResponse)
async def get_grab_logs(
    limit: int = Query(20, ge=1, le=1000, description="Số lượng logs"),
    skip: int = Query(0, ge=0, description="Số logs bỏ qua"),
    start_date: Optional[str] = Query(None, description="Ngày bắt đầu (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)"),
    end_date: Optional[str] = Query(None, description="Ngày kết thúc (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)"),
    transaction_ids: Optional[str] = Query(None, description="Danh sách transaction IDs, phân cách bằng dấu phẩy")
):
    """Lấy danh sách API logs của GrabApi"""
    try:
        db = get_database()
        collection = db["logs_request_api"]
        
        # Tạo filter query với merchant_name fix cứng
        query = {"merchant_name": "GrabApi"}
        
        # Filter transaction_ids (in list)
        if transaction_ids:
            try:
                tx_list = [tx.strip() for tx in transaction_ids.split(",") if tx.strip()]
                if tx_list:
                    query["transaction_id"] = {"$in": tx_list}
            except Exception:
                raise HTTPException(status_code=400, detail="transaction_ids format không hợp lệ")
        
        # Filter theo ngày (between)
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    # Hỗ trợ cả YYYY-MM-DD và YYYY-MM-DD HH:MM:SS
                    if len(start_date) == 10:  # YYYY-MM-DD
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                    else:  # YYYY-MM-DD HH:MM:SS
                        start_dt = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                    date_filter["$gte"] = start_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="start_date phải có format YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS")
            
            if end_date:
                try:
                    if len(end_date) == 10:  # YYYY-MM-DD
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
                        # Thêm 1 ngày để include cả ngày end_date
                        from datetime import timedelta
                        end_dt = end_dt + timedelta(days=1)
                    else:  # YYYY-MM-DD HH:MM:SS
                        end_dt = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                    date_filter["$lt"] = end_dt
                except ValueError:
                    raise HTTPException(status_code=400, detail="end_date phải có format YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS")
            
            if date_filter:
                query["created"] = date_filter
        
        # Đếm tổng số logs
        total = await collection.count_documents(query)
        
        # Lấy logs với pagination, sắp xếp theo created mới nhất
        # Chỉ select 3 trường cần thiết: response_data, request_data, created
        projection = {
            "response_data": 1,
            "request_data": 1,
            "created": 1,
            "_id": 0  # Loại bỏ _id
        }
        cursor = collection.find(query, projection).sort("created", -1).skip(skip).limit(limit)
        logs = await cursor.to_list(length=limit)
        
        # Làm phẳng dữ liệu và map vào từng gift
        flattened_logs = []
        for log in logs:
            # Lấy thông tin chung
            created = log.get("created")

            # Lấy request data từ gift
            request_gift_data = {}
            request_data = log.get("request_data", {})
            if isinstance(request_data, dict) and "request_data" in request_data:
                inner_request = request_data["request_data"]
                if isinstance(inner_request, dict) and "order" in inner_request:
                    order = inner_request["order"]
                    if isinstance(order, dict) and "gift" in order:
                        gift = order["gift"]
                        # Lấy tất cả thông tin từ gift
                        for key, value in gift.items():
                            request_gift_data[key] = value

            # Lấy response_buy data
            response_buy_data = {}
            response_data = log.get("response_data", {})
            if isinstance(response_data, dict) and "response_data" in response_data:
                inner_response = response_data["response_data"]
                if isinstance(inner_response, dict):
                    # Lấy response_buy
                    if "response_buy" in inner_response:
                        response_buy = inner_response["response_buy"]
                        if isinstance(response_buy, dict):
                            response_buy_data = response_buy

                    # Lấy gifts và map thông tin vào từng gift
                    if "response_gift_codes" in inner_response:
                        gift_codes = inner_response["response_gift_codes"]
                        if isinstance(gift_codes, dict) and "gifts" in gift_codes:
                            gifts = gift_codes["gifts"]
                            if isinstance(gifts, list):
                                # Map thông tin vào từng gift
                                for gift in gifts:
                                    if isinstance(gift, dict):
                                        # Bỏ trường url
                                        if "url" in gift:
                                            del gift["url"]

                                        # Thêm created
                                        gift["created"] = created

                                        # Thêm request data với prefix
                                        for key, value in request_gift_data.items():
                                            if key == "inventories" and isinstance(value, list):
                                                # Join inventories array thành string
                                                gift[f"request_{key}"] = ", ".join(value)
                                            else:
                                                gift[f"request_{key}"] = value

                                        # Thêm response_buy data với prefix
                                        for key, value in response_buy_data.items():
                                            gift[f"response_buy_{key}"] = value

                                        # Thêm gift vào kết quả
                                        flattened_logs.append(gift)

        serialized_logs = flattened_logs
        
        return ListResponse(
            success=True,
            message=f"Lấy thành công {len(serialized_logs)} Grab API logs",
            data=serialized_logs,
            total=total,
            count=len(serialized_logs),
            pagination={
                "skip": skip,
                "limit": limit,
                "has_more": skip + len(serialized_logs) < total
            }
        )
        
    except HTTPException:
        raise
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")

@grab_router.get("/stats/", response_model=Dict[str, Any])
async def get_grab_stats():
    """Lấy thống kê nhanh cho GrabApi"""
    try:
        db = get_database()
        collection = db["logs_request_api"]
        
        query = {"merchant_name": "GrabApi"}
        
        # Thống kê cơ bản
        total_requests = await collection.count_documents(query)
        success_requests = await collection.count_documents({**query, "response_code": {"$lt": 400}})
        error_requests = await collection.count_documents({**query, "response_code": {"$gte": 400}})
        
        # Thống kê hôm nay
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_requests = await collection.count_documents({
            **query,
            "created": {"$gte": today}
        })
        
        success_rate = (success_requests / total_requests * 100) if total_requests > 0 else 0
        
        stats = {
            "merchant_name": "GrabApi",
            "total_requests": total_requests,
            "success_requests": success_requests,
            "error_requests": error_requests,
            "success_rate": round(success_rate, 2),
            "today_requests": today_requests
        }
        
        return {
            "success": True,
            "message": "Lấy thống kê GrabApi thành công",
            "data": stats
        }
        
    except PyMongoError as e:
        logger.error(f"MongoDB error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"Lỗi không xác định: {str(e)}")
