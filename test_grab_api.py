#!/usr/bin/env python3
"""
Script để test Grab API riêng
Chạy: python test_grab_api.py
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8000/api/v1"

def test_grab_endpoint(endpoint, params=None):
    """Test Grab API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        response = requests.get(url, params=params)
        
        print(f"\n{'='*60}")
        print(f"Testing: GET {endpoint}")
        print(f"URL: {response.url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'N/A')}")
            
            if 'data' in result and result['data']:
                if isinstance(result['data'], list):
                    print(f"Records returned: {len(result['data'])}")
                    if result['data']:
                        print("Sample record keys:", list(result['data'][0].keys()))
                        # Hiển thị transaction_id của record đầu tiên
                        if 'transaction_id' in result['data'][0]:
                            print(f"First transaction_id: {result['data'][0]['transaction_id']}")
                elif isinstance(result['data'], dict):
                    print("Data keys:", list(result['data'].keys()))
            
            if 'total' in result:
                print(f"Total records: {result['total']}")
                
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Không thể kết nối tới {url}")
        print("Hãy đảm bảo server đang chạy trên port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🚀 Testing Grab API - Dedicated for GrabApi merchant")
    print("Đảm bảo server đang chạy: uvicorn main:app --reload")
    
    # Test 1: Lấy tất cả Grab logs (limit 5)
    test_grab_endpoint("/grab/logs/", params={"limit": 5})
    
    # Test 2: Thống kê Grab
    test_grab_endpoint("/grab/stats/")
    
    # Test 3: Filter theo khoảng thời gian
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    today = datetime.now().strftime("%Y-%m-%d")
    test_grab_endpoint("/grab/logs/", params={
        "limit": 3,
        "start_date": yesterday,
        "end_date": today
    })
    
    # Test 4: Filter theo transaction_ids (sử dụng transaction_id từ test đầu tiên)
    print("\n" + "="*60)
    print("Lấy transaction_id từ logs để test filter...")
    
    try:
        # Lấy một vài transaction_ids thực tế
        response = requests.get(f"{BASE_URL}/grab/logs/", params={"limit": 3})
        if response.status_code == 200:
            result = response.json()
            if result.get('data') and len(result['data']) > 0:
                tx_ids = [log['transaction_id'] for log in result['data'][:2]]
                tx_ids_str = ",".join(tx_ids)
                print(f"Testing với transaction_ids: {tx_ids_str}")
                
                test_grab_endpoint("/grab/logs/", params={
                    "limit": 5,
                    "transaction_ids": tx_ids_str
                })
            else:
                print("Không có dữ liệu để test transaction_ids")
        else:
            print("Không thể lấy transaction_ids để test")
    except Exception as e:
        print(f"Lỗi khi test transaction_ids: {e}")
    
    # Test 5: Kết hợp filters
    test_grab_endpoint("/grab/logs/", params={
        "limit": 2,
        "start_date": yesterday,
        "end_date": today
    })
    
    print(f"\n{'='*60}")
    print("✅ Testing Grab API completed!")
    print("\n📚 Grab API endpoints:")
    print("   👉 GET /api/v1/grab/logs/ - Lấy logs GrabApi")
    print("   👉 GET /api/v1/grab/stats/ - Thống kê GrabApi")
    print("\n🔍 Grab API filters:")
    print("   • start_date, end_date: YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS")
    print("   • transaction_ids: danh sách phân cách bằng dấu phẩy")
    print("   • merchant_name: fix cứng = 'GrabApi'")
    print("\n📖 Documentation: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
