#!/usr/bin/env python3
"""
Script để test response format mới (chỉ 3 trường)
Chạy: python test_response_format.py
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8002/api/v1"

def test_response_format(endpoint, params=None, description=""):
    """Test response format"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        response = requests.get(url, params=params)
        
        print(f"\n{'='*60}")
        print(f"Test: {description}")
        print(f"URL: {response.url}")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success: {result.get('success', False)}")
            print(f"Message: {result.get('message', 'N/A')}")
            
            if 'data' in result and result['data'] and len(result['data']) > 0:
                first_record = result['data'][0]
                print(f"Records returned: {len(result['data'])}")
                print(f"Fields in first record: {list(first_record.keys())}")
                
                # Kiểm tra 3 trường bắt buộc
                required_fields = ['response_data', 'request_data', 'created']
                missing_fields = [field for field in required_fields if field not in first_record]
                extra_fields = [field for field in first_record.keys() if field not in required_fields]
                
                if missing_fields:
                    print(f"❌ Missing fields: {missing_fields}")
                else:
                    print("✅ All required fields present")
                
                if extra_fields:
                    print(f"⚠️  Extra fields: {extra_fields}")
                else:
                    print("✅ No extra fields")
                
                # Hiển thị sample data
                print(f"Sample created: {first_record.get('created', 'N/A')}")
                
                if 'request_data' in first_record:
                    req_keys = list(first_record['request_data'].keys()) if isinstance(first_record['request_data'], dict) else "Not dict"
                    print(f"Request data keys: {req_keys}")
                
                if 'response_data' in first_record:
                    resp_keys = list(first_record['response_data'].keys()) if isinstance(first_record['response_data'], dict) else "Not dict"
                    print(f"Response data keys: {resp_keys}")
            
            if 'total' in result:
                print(f"Total records: {result['total']}")
                
        else:
            print(f"❌ Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Không thể kết nối tới {url}")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("🧪 Testing Response Format - Chỉ 3 trường: response_data, request_data, created")
    print("Đảm bảo server đang chạy: uvicorn main:app --reload")
    
    # Test 1: General API
    test_response_format("/logs/", 
                        params={"limit": 2, "merchant_name": "VmgApi"}, 
                        description="General API - VmgApi")
    
    # Test 2: Grab API
    test_response_format("/grab/logs/", 
                        params={"limit": 2}, 
                        description="Grab API")
    
    # Test 3: General API - GrabApi
    test_response_format("/logs/", 
                        params={"limit": 2, "merchant_name": "GrabApi"}, 
                        description="General API - GrabApi")
    
    # Test 4: Filter theo transaction_ids
    test_response_format("/grab/logs/", 
                        params={"limit": 1, "transaction_ids": "UB20250625178994426942335"}, 
                        description="Grab API - Filter by transaction_id")
    
    print(f"\n{'='*60}")
    print("✅ Response format testing completed!")
    print("\n📋 Expected response format:")
    print("   • response_data: object")
    print("   • request_data: object") 
    print("   • created: datetime string")
    print("   • NO _id field")
    print("   • NO other fields")

if __name__ == "__main__":
    main()
